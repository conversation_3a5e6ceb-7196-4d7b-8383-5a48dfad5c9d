'use client';

import React, { useState, useEffect } from 'react';
import { 
  MagnifyingGlassIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon,
  ArrowPathIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface BrowsingSubtask {
  id: string;
  type: 'search' | 'navigate' | 'extract' | 'analyze';
  description: string;
  query: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  priority: number;
  attempts: number;
  maxAttempts: number;
  searchTerms?: string[];
  expectedInfo?: string;
  result?: any;
  error?: string;
}

interface BrowsingPlan {
  id: string;
  goal: string;
  subtasks: BrowsingSubtask[];
  progress: number;
  status: 'planning' | 'executing' | 'completed' | 'failed';
  currentSubtask?: string;
  completedSubtasks: string[];
  failedSubtasks: string[];
}

interface BrowsingProgressIndicatorProps {
  plan?: BrowsingPlan;
  currentStatus?: string;
  isVisible?: boolean;
  className?: string;
}

const getTaskIcon = (type: string, status: string) => {
  const iconClass = "w-4 h-4";
  
  if (status === 'completed') {
    return <CheckCircleIcon className={`${iconClass} text-green-500`} />;
  } else if (status === 'failed') {
    return <XCircleIcon className={`${iconClass} text-red-500`} />;
  } else if (status === 'in_progress') {
    return <ArrowPathIcon className={`${iconClass} text-blue-500 animate-spin`} />;
  }
  
  switch (type) {
    case 'search':
      return <MagnifyingGlassIcon className={`${iconClass} text-gray-400`} />;
    case 'navigate':
      return <GlobeAltIcon className={`${iconClass} text-gray-400`} />;
    case 'extract':
      return <DocumentTextIcon className={`${iconClass} text-gray-400`} />;
    case 'analyze':
      return <ChartBarIcon className={`${iconClass} text-gray-400`} />;
    default:
      return <ClockIcon className={`${iconClass} text-gray-400`} />;
  }
};

const getStatusCheckbox = (status: string) => {
  switch (status) {
    case 'completed':
      return '[x]';
    case 'in_progress':
      return '[/]';
    case 'failed':
      return '[-]';
    default:
      return '[ ]';
  }
};

export default function BrowsingProgressIndicator({
  plan,
  currentStatus,
  isVisible = true,
  className = ''
}: BrowsingProgressIndicatorProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0);

  // Animate progress bar
  useEffect(() => {
    if (plan?.progress !== undefined) {
      const timer = setTimeout(() => {
        setAnimatedProgress(plan.progress);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [plan?.progress]);

  if (!isVisible || !plan) {
    return null;
  }

  return (
    <div className={`bg-gray-800/60 backdrop-blur-sm border border-gray-600/30 rounded-xl p-4 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <GlobeAltIcon className="w-5 h-5 text-blue-400" />
          <h3 className="text-sm font-semibold text-white">Smart Browsing</h3>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-400">
            {plan.completedSubtasks.length}/{plan.subtasks.length} completed
          </span>
          <div className="text-xs text-blue-400 font-medium">
            {Math.round(animatedProgress)}%
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700/50 rounded-full h-2">
        <div 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${animatedProgress}%` }}
        />
      </div>

      {/* Goal */}
      {plan.goal && (
        <div className="bg-gray-700/30 rounded-lg p-3">
          <p className="text-xs text-gray-300 mb-1">Goal:</p>
          <p className="text-sm text-white">{plan.goal}</p>
        </div>
      )}

      {/* Current Status */}
      {currentStatus && (
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <ArrowPathIcon className="w-4 h-4 text-blue-400 animate-spin" />
            <p className="text-sm text-blue-300">{currentStatus}</p>
          </div>
        </div>
      )}

      {/* Todo List */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">Todo List</h4>
        <div className="space-y-1.5">
          {plan.subtasks.map((task, index) => (
            <div 
              key={task.id}
              className={`flex items-start space-x-3 p-2 rounded-lg transition-all duration-200 ${
                task.status === 'in_progress' 
                  ? 'bg-blue-500/10 border border-blue-500/20' 
                  : task.status === 'completed'
                  ? 'bg-green-500/10 border border-green-500/20'
                  : task.status === 'failed'
                  ? 'bg-red-500/10 border border-red-500/20'
                  : 'bg-gray-700/20'
              }`}
            >
              {/* Task Icon */}
              <div className="flex-shrink-0 mt-0.5">
                {getTaskIcon(task.type, task.status)}
              </div>

              {/* Task Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start space-x-2">
                  <span className={`text-xs font-mono ${
                    task.status === 'completed' ? 'text-green-400' :
                    task.status === 'failed' ? 'text-red-400' :
                    task.status === 'in_progress' ? 'text-blue-400' :
                    'text-gray-500'
                  }`}>
                    {getStatusCheckbox(task.status)}
                  </span>
                  <p className={`text-sm flex-1 ${
                    task.status === 'completed' ? 'text-green-300 line-through' :
                    task.status === 'failed' ? 'text-red-300 line-through' :
                    task.status === 'in_progress' ? 'text-blue-300 font-medium' :
                    'text-gray-300'
                  }`}>
                    {task.description}
                  </p>
                </div>

                {/* Query Info */}
                {task.query && task.status === 'in_progress' && (
                  <div className="mt-1 ml-6">
                    <p className="text-xs text-gray-400">
                      Searching: <span className="text-blue-300">"{task.query}"</span>
                    </p>
                  </div>
                )}

                {/* Error Info */}
                {task.error && task.status === 'failed' && (
                  <div className="mt-1 ml-6">
                    <p className="text-xs text-red-400">
                      Error: {task.error}
                    </p>
                  </div>
                )}

                {/* Retry Info */}
                {task.attempts > 0 && task.status !== 'completed' && (
                  <div className="mt-1 ml-6">
                    <p className="text-xs text-yellow-400">
                      Attempt {task.attempts}/{task.maxAttempts}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary */}
      {plan.status === 'completed' && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-4 h-4 text-green-400" />
            <p className="text-sm text-green-300">Browsing completed successfully!</p>
          </div>
        </div>
      )}

      {plan.status === 'failed' && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <XCircleIcon className="w-4 h-4 text-red-400" />
            <p className="text-sm text-red-300">Browsing failed. Please try again.</p>
          </div>
        </div>
      )}
    </div>
  );
}
