import { NextRequest, NextResponse } from 'next/server';
import BrowserlessService from '@/lib/browserless';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, url, query, searchEngine = 'google', extractionType = 'content', customSelector, timeout } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    const browserless = BrowserlessService.getInstance();
    let result;

    switch (action) {
      case 'navigate':
        if (!url) {
          return NextResponse.json(
            { error: 'URL is required for navigate action' },
            { status: 400 }
          );
        }
        result = await browserless.navigateAndExtract(url, customSelector);
        break;

      case 'search':
        if (!query) {
          return NextResponse.json(
            { error: 'Query is required for search action' },
            { status: 400 }
          );
        }
        result = await browserless.searchAndExtractUnblocked(query, searchEngine);
        break;

      case 'screenshot':
        if (!url) {
          return NextResponse.json(
            { error: 'URL is required for screenshot action' },
            { status: 400 }
          );
        }
        result = await browserless.takeScreenshot(url);
        break;

      case 'custom':
        if (!url) {
          return NextResponse.json(
            { error: 'URL is required for custom action' },
            { status: 400 }
          );
        }
        
        // Custom browsing with user-provided code
        const customCode = body.code;
        if (!customCode) {
          return NextResponse.json(
            { error: 'Code is required for custom action' },
            { status: 400 }
          );
        }

        result = await browserless.executeFunction(customCode, { url, ...body.context }, { timeout });
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      action,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Web browsing API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Web browsing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check service status and stats
export async function GET() {
  try {
    const browserless = BrowserlessService.getInstance();
    const stats = browserless.getStats();

    return NextResponse.json({
      status: 'operational',
      service: 'web-browsing',
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Web browsing status check error:', error);
    
    return NextResponse.json(
      { 
        status: 'error',
        service: 'web-browsing',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
